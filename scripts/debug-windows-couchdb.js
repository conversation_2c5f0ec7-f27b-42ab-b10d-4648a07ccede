#!/usr/bin/env node

/**
 * Windows CouchDB Debug Script
 * 
 * This script helps diagnose Windows CouchDB startup issues by:
 * 1. Checking binary integrity and permissions
 * 2. Verifying environment setup
 * 3. Testing batch file execution
 * 4. Providing detailed diagnostics
 */

const fs = require('fs');
const path = require('path');
const { spawn, execSync } = require('child_process');
const os = require('os');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(60));
  log(title, 'cyan');
  console.log('='.repeat(60));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function checkWindowsCouchDB() {
  logSection('Windows CouchDB Diagnostic Tool');
  
  if (process.platform !== 'win32') {
    logWarning('This script is designed for Windows. Current platform: ' + process.platform);
    return false;
  }

  const couchdbPath = path.join(__dirname, '..', 'electron', 'resources', 'couchdb-windows');
  
  // 1. Check if CouchDB directory exists
  logSection('1. Directory Structure Check');
  
  if (!fs.existsSync(couchdbPath)) {
    logError(`CouchDB directory not found: ${couchdbPath}`);
    return false;
  }
  
  logSuccess(`CouchDB directory found: ${couchdbPath}`);
  
  // Check directory size
  try {
    const stats = fs.statSync(couchdbPath);
    logInfo(`Directory created: ${stats.birthtime}`);
    logInfo(`Directory modified: ${stats.mtime}`);
  } catch (error) {
    logWarning(`Could not get directory stats: ${error.message}`);
  }

  // 2. Check critical files
  logSection('2. Critical Files Check');
  
  const criticalFiles = [
    'bin/couchdb.cmd',
    'bin/couchdb',
    'bin/couchjs.exe',
    'etc/default.ini',
    'etc/vm.args',
    'releases/start_erl.data',
    'releases/couchdb.boot'
  ];

  let missingFiles = [];
  
  for (const file of criticalFiles) {
    const filePath = path.join(couchdbPath, file);
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      logSuccess(`${file} (${stats.size} bytes)`);
    } else {
      logError(`Missing: ${file}`);
      missingFiles.push(file);
    }
  }

  if (missingFiles.length > 0) {
    logError(`${missingFiles.length} critical files missing!`);
    return false;
  }

  // 3. Check Erlang runtime
  logSection('3. Erlang Runtime Check');
  
  const ertsDirectories = fs.readdirSync(couchdbPath).filter(d => d.startsWith('erts-'));
  
  if (ertsDirectories.length === 0) {
    logError('No Erlang runtime directories found (erts-*)');
    return false;
  }
  
  if (ertsDirectories.length > 1) {
    logWarning(`Multiple Erlang runtimes found: ${ertsDirectories.join(', ')}`);
  }
  
  const ertsDir = ertsDirectories[0];
  logSuccess(`Erlang runtime found: ${ertsDir}`);
  
  // Check start_erl.data consistency
  const startErlPath = path.join(couchdbPath, 'releases', 'start_erl.data');
  const startErlContent = fs.readFileSync(startErlPath, 'utf8').trim();
  const expectedVersion = ertsDir.replace('erts-', '');
  
  if (startErlContent.startsWith(expectedVersion)) {
    logSuccess(`Erlang version consistent: ${expectedVersion}`);
  } else {
    logError(`Erlang version mismatch! start_erl.data: "${startErlContent}", directory: "${expectedVersion}"`);
  }

  // Check Erlang binaries
  const erlangBinPath = path.join(couchdbPath, ertsDir, 'bin');
  const erlangBinaries = ['erl.exe', 'epmd.exe', 'beam.smp.dll'];
  
  for (const binary of erlangBinaries) {
    const binaryPath = path.join(erlangBinPath, binary);
    if (fs.existsSync(binaryPath)) {
      logSuccess(`Erlang binary: ${binary}`);
    } else {
      logError(`Missing Erlang binary: ${binary}`);
    }
  }

  // 4. Test batch file execution
  logSection('4. Batch File Execution Test');
  
  const batchFile = path.join(couchdbPath, 'bin', 'couchdb.cmd');
  
  try {
    // Test if we can read the batch file
    const batchContent = fs.readFileSync(batchFile, 'utf8');
    logSuccess('Batch file is readable');
    
    // Check for problematic characters
    if (batchContent.includes('\r\n')) {
      logSuccess('Batch file has Windows line endings');
    } else {
      logWarning('Batch file might have Unix line endings');
    }
    
  } catch (error) {
    logError(`Cannot read batch file: ${error.message}`);
    return false;
  }

  // 5. Environment simulation
  logSection('5. Environment Simulation');
  
  // Simulate the environment that would be set up
  const simulatedEnv = {
    ROOTDIR: couchdbPath,
    COUCHDB_BIN_DIR: path.join(couchdbPath, 'bin'),
    COUCHDB_LIB_DIR: path.join(couchdbPath, 'lib'),
    COUCHDB_ETC_DIR: path.join(couchdbPath, 'etc'),
    ERLANG_HOME: path.join(couchdbPath, ertsDir),
    BINDIR: path.join(couchdbPath, ertsDir, 'bin'),
    EMU: 'beam',
    PROGNAME: 'couchdb'
  };

  logInfo('Simulated environment:');
  for (const [key, value] of Object.entries(simulatedEnv)) {
    const exists = fs.existsSync(value);
    const status = exists ? '✅' : '❌';
    console.log(`  ${status} ${key}=${value}`);
  }

  // 6. Path analysis
  logSection('6. Path Analysis');
  
  // Check for spaces in paths
  if (couchdbPath.includes(' ')) {
    logWarning(`CouchDB path contains spaces: "${couchdbPath}"`);
    logInfo('This might require the portable copy workaround');
  } else {
    logSuccess('CouchDB path has no spaces');
  }

  // Check path length
  if (couchdbPath.length > 260) {
    logWarning(`Path is very long (${couchdbPath.length} chars), might hit Windows limits`);
  } else {
    logSuccess(`Path length OK (${couchdbPath.length} chars)`);
  }

  logSection('7. Summary');
  logSuccess('Windows CouchDB diagnostic completed');
  logInfo('If issues persist, check the generated log files in the database directory');
  
  return true;
}

// Run the diagnostic
checkWindowsCouchDB().catch(error => {
  logError(`Diagnostic failed: ${error.message}`);
  process.exit(1);
});
