#!/usr/bin/env node

/**
 * Windows CouchDB Fix Validation Script
 * 
 * This script tests the Windows CouchDB fixes by:
 * 1. Simulating the startup process
 * 2. Validating environment setup
 * 3. Testing process spawning
 * 4. Verifying connectivity
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const http = require('http');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(60));
  log(title, 'cyan');
  console.log('='.repeat(60));
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

async function testWindowsCouchDBFix() {
  logSection('Windows CouchDB Fix Validation');
  
  if (process.platform !== 'win32') {
    logWarning('This test is designed for Windows. Current platform: ' + process.platform);
    logInfo('The fixes should work on Windows when deployed');
    return true;
  }

  const couchdbPath = path.join(__dirname, '..', 'electron', 'resources', 'couchdb-windows');
  
  // 1. Validate fix prerequisites
  logSection('1. Fix Prerequisites Validation');
  
  if (!fs.existsSync(couchdbPath)) {
    logError(`CouchDB directory not found: ${couchdbPath}`);
    return false;
  }
  
  logSuccess(`CouchDB directory found: ${couchdbPath}`);

  // 2. Test Erlang runtime detection
  logSection('2. Erlang Runtime Detection Test');
  
  const erlangDirs = fs.readdirSync(couchdbPath).filter(d => d.startsWith('erts-'));
  
  if (erlangDirs.length === 0) {
    logError('No Erlang runtime directories found');
    return false;
  }
  
  const erlangDir = erlangDirs[0];
  logSuccess(`Erlang runtime detected: ${erlangDir}`);
  
  // Test start_erl.data consistency
  const startErlPath = path.join(couchdbPath, 'releases', 'start_erl.data');
  if (fs.existsSync(startErlPath)) {
    const startErlContent = fs.readFileSync(startErlPath, 'utf8').trim();
    const expectedVersion = erlangDir.replace('erts-', '');
    const fileVersion = startErlContent.split(' ')[0];
    
    if (fileVersion === expectedVersion) {
      logSuccess(`Erlang version consistency verified: ${expectedVersion}`);
    } else {
      logWarning(`Version mismatch detected (will be handled by fix): file=${fileVersion}, dir=${expectedVersion}`);
    }
  }

  // 3. Test environment setup simulation
  logSection('3. Environment Setup Simulation');
  
  const erlangHomeDir = path.join(couchdbPath, erlangDir);
  const erlangBinDir = path.join(erlangHomeDir, 'bin');
  const couchBinDir = path.join(couchdbPath, 'bin');
  
  // Simulate the environment that would be set up
  const simulatedEnv = {
    ROOTDIR: couchdbPath,
    COUCHDB_BIN_DIR: couchBinDir,
    ERLANG_HOME: erlangHomeDir,
    BINDIR: erlangBinDir,
    EMU: 'beam',
    PROGNAME: 'couchdb'
  };

  let envValid = true;
  for (const [key, value] of Object.entries(simulatedEnv)) {
    if (fs.existsSync(value)) {
      logSuccess(`${key}: ${value}`);
    } else {
      logError(`${key}: ${value} (NOT FOUND)`);
      envValid = false;
    }
  }

  if (!envValid) {
    logError('Environment setup validation failed');
    return false;
  }

  // 4. Test critical binaries
  logSection('4. Critical Binaries Test');
  
  const criticalBinaries = [
    { name: 'erl.exe', path: path.join(erlangBinDir, 'erl.exe') },
    { name: 'epmd.exe', path: path.join(erlangBinDir, 'epmd.exe') },
    { name: 'couchjs.exe', path: path.join(couchBinDir, 'couchjs.exe') }
  ];
  
  let binariesValid = true;
  for (const binary of criticalBinaries) {
    if (fs.existsSync(binary.path)) {
      const stats = fs.statSync(binary.path);
      logSuccess(`${binary.name}: ${binary.path} (${stats.size} bytes)`);
    } else {
      logError(`${binary.name}: ${binary.path} (NOT FOUND)`);
      binariesValid = false;
    }
  }

  if (!binariesValid) {
    logError('Critical binaries validation failed');
    return false;
  }

  // 5. Test path handling
  logSection('5. Path Handling Test');
  
  if (couchdbPath.includes(' ')) {
    logWarning(`Path contains spaces: "${couchdbPath}"`);
    logInfo('Fix will create portable copy to handle this');
    
    // Simulate portable path creation
    const portablePath = path.join(process.env.USERPROFILE || 'C:\\Users\\<USER>\n🎉 Windows CouchDB fix validation completed successfully!', 'green');
    process.exit(0);
  } else {
    log('\n💥 Windows CouchDB fix validation failed!', 'red');
    process.exit(1);
  }
}).catch(error => {
  logError(`Validation failed with error: ${error.message}`);
  process.exit(1);
});
