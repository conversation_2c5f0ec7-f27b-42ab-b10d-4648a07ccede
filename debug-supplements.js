/**
 * Debug script to investigate supplement issues
 */

// Import required modules
const path = require('path');
const fs = require('fs');

// Check if we're in the right directory
const currentDir = process.cwd();
console.log('Current directory:', currentDir);

// Check if the lib directory exists
const libPath = path.join(currentDir, 'lib');
if (!fs.existsSync(libPath)) {
  console.error('❌ lib directory not found. Make sure you are in the project root.');
  process.exit(1);
}

// Try to import modules with better error handling
let databaseV4, getMenu, createSupplement, getAllSupplements, getCategorySupplementPrices;

try {
  ({ databaseV4 } = require('./lib/db/v4/core/db-instance'));
  ({ getMenu } = require('./lib/db/v4/operations/menu-ops'));
  ({ createSupplement, getAllSupplements, getCategorySupplementPrices } = require('./lib/db/v4/operations/supplement-ops'));
  console.log('✅ Modules imported successfully');
} catch (error) {
  console.error('❌ Error importing modules:', error.message);
  process.exit(1);
}

async function debugSupplements() {
  console.log('🔍 Starting supplement debugging...');
  
  try {
    // Initialize database
    console.log('📊 Initializing database...');
    await databaseV4.initialize();
    
    // Get current menu
    console.log('📋 Getting current menu...');
    const menu = await getMenu();
    
    console.log('📊 Menu categories found:', menu.categories.length);
    
    // Check each category for supplements
    for (const category of menu.categories) {
      console.log(`\n📂 Category: ${category.name} (ID: ${category.id})`);
      console.log(`   Items: ${category.items?.length || 0}`);
      console.log(`   Supplements: ${category.supplements?.length || 0}`);
      
      if (category.supplements && category.supplements.length > 0) {
        console.log('   📋 Existing supplements:');
        category.supplements.forEach((supplement, index) => {
          console.log(`     ${index + 1}. ${supplement.name} (ID: ${supplement.id})`);
          console.log(`        Active: ${supplement.isActive !== false}`);
          console.log(`        Stock consumption: ${supplement.stockConsumption ? 'Yes' : 'No'}`);
        });
      }
      
      // Check supplement configuration
      if (category.supplementConfig) {
        console.log('   ⚙️ Supplement config:');
        console.log(`     Enabled: ${category.supplementConfig.isEnabled !== false}`);
        console.log(`     Global pricing: ${JSON.stringify(category.supplementConfig.globalPricing)}`);
      } else {
        console.log('   ⚠️ No supplement configuration found');
      }
      
      // Try to get supplements using the operations
      try {
        const supplements = await getAllSupplements(category.id);
        console.log(`   🔍 getAllSupplements returned: ${supplements.length} supplements`);
        
        const prices = await getCategorySupplementPrices(category.id);
        console.log(`   💰 Supplement prices: ${JSON.stringify(prices)}`);
      } catch (error) {
        console.log(`   ❌ Error getting supplements: ${error.message}`);
      }
    }
    
    // Try to create a test supplement in the first category
    if (menu.categories.length > 0) {
      const firstCategory = menu.categories[0];
      console.log(`\n🧪 Creating test supplement in category: ${firstCategory.name}`);
      
      try {
        const testSupplement = await createSupplement(firstCategory.id, {
          name: 'Test Supplement',
          description: 'A test supplement for debugging',
          isActive: true
        });
        
        console.log('✅ Test supplement created successfully:', testSupplement.id);
        
        // Check if it appears in the category now
        const updatedMenu = await getMenu();
        const updatedCategory = updatedMenu.categories.find(c => c.id === firstCategory.id);
        console.log(`📊 Category now has ${updatedCategory.supplements?.length || 0} supplements`);
        
      } catch (error) {
        console.log('❌ Error creating test supplement:', error.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Debug script error:', error);
  }
}

// Run the debug script
debugSupplements().then(() => {
  console.log('🏁 Debug script completed');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Debug script failed:', error);
  process.exit(1);
});
