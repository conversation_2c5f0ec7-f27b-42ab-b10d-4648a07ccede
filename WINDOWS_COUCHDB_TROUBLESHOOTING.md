# 🪟 Windows CouchDB Troubleshooting Guide

## 🔍 Problem Overview

CouchDB works perfectly on macOS but fails to start on Windows in the Electron release. This guide provides comprehensive troubleshooting steps and solutions.

## 🚨 Common Issues Identified

### 1. **Path Handling Problems**
- Windows paths with spaces cause batch script failures
- Mixed path separators (forward vs backslash)
- Long path names exceeding Windows limits

### 2. **Environment Variable Issues**
- Incorrect ROOTDIR setup
- Missing or wrong Erlang environment variables
- PATH construction problems

### 3. **Process Spawning Issues**
- Wrong executable being called
- Incorrect working directory
- Missing shell configuration

### 4. **Binary Integrity Problems**
- Missing or corrupted CouchDB binaries
- Incomplete Erlang runtime
- Missing DLL dependencies

## 🛠️ Diagnostic Tools

### Quick Diagnostic
```bash
npm run debug:windows-couchdb
```

This script checks:
- ✅ Directory structure and file integrity
- ✅ Erlang runtime consistency
- ✅ Critical binary existence
- ✅ Path analysis and environment simulation

### Manual Verification
```bash
# Check if CouchDB binaries exist
ls electron/resources/couchdb-windows/bin/

# Verify Erlang runtime
ls electron/resources/couchdb-windows/erts-*/bin/

# Test batch file manually (Windows only)
cd electron/resources/couchdb-windows
bin\couchdb.cmd --help
```

## 🔧 Solutions Implemented

### 1. **Enhanced Path Handling**
- Automatic detection of paths with spaces
- Portable copy creation to space-free location
- Proper Windows path separator usage

### 2. **Improved Environment Setup**
- Direct erl.exe execution (bypassing batch file issues)
- Exact environment variable matching with couchdb.cmd
- Proper Erlang version detection and consistency

### 3. **Better Process Spawning**
- Use erl.exe directly instead of couchdb.cmd
- Correct working directory setup
- Proper argument passing to Erlang VM

### 4. **Enhanced Error Reporting**
- Windows-specific error detection
- Detailed debug logging
- Comprehensive troubleshooting guidance

## 📋 Step-by-Step Troubleshooting

### Step 1: Run Diagnostic
```bash
npm run debug:windows-couchdb
```

Look for:
- ❌ Missing files
- ⚠️ Path issues
- ❌ Environment problems

### Step 2: Check Debug Logs
After attempting to start the app, check:
```
<userData>/pouchdb-data/windows-couchdb-debug.log
<userData>/pouchdb-data/couchdb-stdout.log
<userData>/pouchdb-data/couchdb-stderr.log
```

### Step 3: Common Fixes

#### Fix 1: Antivirus Interference
```bash
# Add exclusions to Windows Defender:
# - electron/resources/couchdb-windows/
# - %USERPROFILE%/AppData/Roaming/bistro/
```

#### Fix 2: Permission Issues
```bash
# Run as Administrator or fix permissions:
icacls electron\resources\couchdb-windows /grant Users:F /T
```

#### Fix 3: Port Conflicts
```bash
# Check if port 5984 is in use:
netstat -an | findstr :5984

# Kill conflicting processes:
taskkill /F /IM beam.smp.exe
```

#### Fix 4: Missing Dependencies
```bash
# Install Visual C++ Redistributables:
# Download from Microsoft and install latest x64 version
```

## 🔍 Advanced Debugging

### Enable Verbose Logging
Set environment variable before starting:
```cmd
set COUCHDB_LOG_LEVEL=debug
```

### Manual CouchDB Test
```cmd
cd electron\resources\couchdb-windows
set ROOTDIR=%CD%
set ERLANG_HOME=%CD%\erts-14.2.5.9
set PATH=%ERLANG_HOME%\bin;%PATH%
erts-14.2.5.9\bin\erl.exe -boot releases\couchdb -args_file etc\vm.args
```

### Check Windows Event Viewer
1. Open Event Viewer (eventvwr.msc)
2. Navigate to Windows Logs > Application
3. Look for errors related to Erlang or CouchDB

## 🎯 Key Fixes Applied

### 1. **Direct Erlang Execution**
Instead of using `couchdb.cmd`, we now call `erl.exe` directly with the same arguments the batch file would use.

### 2. **Portable Path Handling**
Automatically copies CouchDB to a space-free path when needed.

### 3. **Environment Consistency**
Ensures all environment variables match exactly what `couchdb.cmd` would set.

### 4. **Enhanced Error Detection**
Provides specific guidance based on error types and exit codes.

## 📞 Getting Help

If issues persist:

1. **Collect Debug Information:**
   ```bash
   npm run debug:windows-couchdb > debug-output.txt
   ```

2. **Check Log Files:**
   - `windows-couchdb-debug.log`
   - `couchdb-stdout.log`
   - `couchdb-stderr.log`

3. **System Information:**
   - Windows version
   - Node.js version
   - Antivirus software
   - Any error messages

## ✅ Success Indicators

When CouchDB starts successfully, you should see:
```
✅ CouchDB server started successfully
✅ CouchDB is ready and responding
🚀 CouchDB server started on port 5984
```

The app should then load normally with full database functionality.
